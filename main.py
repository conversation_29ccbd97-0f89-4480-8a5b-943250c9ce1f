import numpy as np
import pandas as pd
import yfinance as yf
from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
from sklearn.preprocessing import MinMaxScaler
from sklearn.cluster import KMeans
from dataclasses import dataclass
from typing import Optional, Dict, List, Any, Tuple
import re  # Regular expression module
from xgboost import XGBClassifier
import lightgbm as lgb
# Import ensemble classifiers from sklearn
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from datetime import datetime
import traceback
import joblib
import os
import logging


# Suppress 'Unknown property box-shadow' warnings
import warnings
warnings.filterwarnings("ignore", message="Unknown property box-shadow")

# Import loading screen manager for external loading screen
from loading_screen import LoadingScreenManager

# Import parameter registry
from parameter_registry import ParameterRegistry, default_registry

# Import data dispatcher for centralized data management
from data_dispatcher import DataDispatcher

# Import dialog manager for background processing
from dialog_manager import DialogManager

# Import Schwab API for price and options data
try:
    from schwab_api import initialize_api
    from login_dialog import LoginDialog
    # Import authentication modules
    from login_page import LoginPage
    import user_management
    SCHWAB_AVAILABLE = True
except ImportError as e:
    print(f"Schwab API components not available: {e}")
    SCHWAB_AVAILABLE = False

# Import Options Analyzer
from option_analyzer import OptionsAnalyzerTab

# Import volatility graph, density graph, and Volatility Statistics tab
from volatility_graph import VolatilityGraphTab
from density_graph import DensityGraphTab
from fwl_odds import FWLOddsTab
from Volatility_Statistics_tab import VolatilityStatisticsTab

# Import DetachableTabWidget for draggable tabs
from detachable_tab_widget import DetachableTabWidget

# Import Data Tab for OHLC data display
from data_tab import DataTab

# Import Market XRAY Tab for options data collection
from market_xray_tab import MarketXRAYTab

# Import TradingView Scraper Tab
from tradingview_scraper_tab import TradingViewScraperTabWidget

# Import Data Warehouse Tab
from data_warehouse_tab import DataWarehouseTab

# Import Seasonality Tab
from seasonality_tab import SeasonalityTab

# Import Game Theory Tab
from game_theory_tab import GameTheoryTab



# Import candlestick chart components
from candlestick_chart import PriceBox, CandlestickItem, VolumeProfileItem, CandlestickChart, CandlestickChartSettingsDialog

# Import crosshair utility for consistent crosshairs across the application
try:
    from crosshair_utility import add_crosshair, add_price_crosshair
except ImportError:
    # Crosshair utility module not available, will use direct implementation
    pass

# Import GEX and OI tab (will be imported dynamically in MainWindow to handle import errors gracefully)

# Import scikit-learn based neural models (no TensorFlow required)
from sklearn_neural_models import GRUClassifier, LSTMClassifier, get_model_for_timeframe, get_model_weights
from feature_engineering import FeatureExtractor
from sequence_modeling import prepare_sequence_data
from signal_processor import SignalProcessor
from pivot_validator import PivotValidator
from signal_display import EnhancedSignalDisplay
# Import enhanced signal markers
from enhanced_signal_markers import SignalPopup
from signal_markers_integration import integrate_with_matplotlib
# Enhanced trendlines import removed
NEURAL_MODELS_AVAILABLE = True

# Configure logging - absolute minimal verbosity, only critical messages
logging.basicConfig(
    level=logging.CRITICAL,  # Changed from ERROR to CRITICAL to minimize verbosity even further
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("fractal_tester.log"),
        # Remove StreamHandler to prevent any console output
    ]
)
logger = logging.getLogger("FractalTester")
logger.setLevel(logging.CRITICAL)

# Set all loggers to CRITICAL level by default (most restrictive)
logging.getLogger().setLevel(logging.CRITICAL)

# Disable all known loggers completely
noisy_modules = [
    'matplotlib', 'PIL', 'yfinance', 'urllib3', 'requests', 'pyqtgraph',
    'pandas', 'numpy', 'sklearn', 'xgboost', 'lightgbm', 'PyQt6',
    'feature_engineering', 'signal_processor', 'pivot_validator', 'parameter_registry',
    'option_analyzer', 'volatility_graph', 'density_graph', 'fwl_odds',
    'market_odds', 'crosshair_utility', 'loading_screen', 'data_tab',
    'candlestick_chart', 'enhanced_signal_markers',

]
for module in noisy_modules:
    logging.getLogger(module).setLevel(logging.CRITICAL)
    # Also disable propagation to prevent logs from bubbling up
    logging.getLogger(module).propagate = False

# Function to control logging when needed (for troubleshooting)
def toggle_logging(enable=False):
    """Toggle logging on/off for troubleshooting

    Args:
        enable (bool): True to enable debug logging, False to disable all logging

    Returns:
        str: Status message
    """
    if enable:
        # Re-enable StreamHandler for console output
        console_handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logging.getLogger().addHandler(console_handler)

        # Set to DEBUG level
        logging.getLogger().setLevel(logging.DEBUG)
        logger.setLevel(logging.DEBUG)

        # Enable propagation for all modules
        for module in noisy_modules:
            logging.getLogger(module).setLevel(logging.DEBUG)
            logging.getLogger(module).propagate = True

        return "Debug logging enabled"
    else:

        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            if not isinstance(handler, logging.FileHandler):
                root_logger.removeHandler(handler)


        logging.getLogger().setLevel(logging.CRITICAL)
        logger.setLevel(logging.CRITICAL)


        for module in noisy_modules:
            logging.getLogger(module).setLevel(logging.CRITICAL)
            logging.getLogger(module).propagate = False

        return "Logging disabled"




from market_odds import PriceLevel, PeakTroughRays, DummyClassifier, VectorRebaseChart, SettingsDialog
from universal_controls import UniversalControlPanel

class MainWindow(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("The System")

        # Force window to be resizable in both directions without any minimum constraints
        self.setSizePolicy(QtWidgets.QSizePolicy.Policy.Preferred, QtWidgets.QSizePolicy.Policy.Preferred)
        self.setMinimumSize(1, 1)  # Allow extremely small sizes

        # Initialize the data dispatcher (singleton)
        self.data_dispatcher = DataDispatcher.get_instance()

        # Initialize dialog manager with settings from parameter registry
        self.init_dialog_manager()

        # Create a central widget to hold both the universal controls and the tab widget
        central_widget = QtWidgets.QWidget()
        central_widget.setSizePolicy(QtWidgets.QSizePolicy.Policy.Preferred, QtWidgets.QSizePolicy.Policy.Preferred)
        central_layout = QtWidgets.QVBoxLayout(central_widget)
        central_layout.setContentsMargins(0, 0, 0, 0)
        central_layout.setSpacing(0)
        central_layout.setSizeConstraint(QtWidgets.QLayout.SizeConstraint.SetNoConstraint)

        # Create the universal control panel
        self.universal_controls = UniversalControlPanel(self)
        central_layout.addWidget(self.universal_controls)

        # Create the detachable tab widget
        self.tab_widget = DetachableTabWidget()
        # Disable tab closing buttons
        self.tab_widget.setTabsClosable(False)
        central_layout.addWidget(self.tab_widget)

        # Set the central widget
        self.setCentralWidget(central_widget)

        # Initialize the symbol input with the default symbol from the registry
        self.universal_controls.symbol_input.setText(default_registry.get_value('symbol'))

        # Market Odds tab: use the existing VectorRebaseChart
        self.market_odds_tab = VectorRebaseChart()
        self.tab_widget.addTab(self.market_odds_tab, "Market Odds")

        # Traditional Candlestick Chart tab
        self.candlestick_tab = CandlestickChart()
        self.tab_widget.addTab(self.candlestick_tab, "Candlestick Chart")

        # Options Analyzer tab: now uses our custom OptionsAnalyzerTab
        self.options_analyzer_tab = OptionsAnalyzerTab(self.tab_widget)
        self.tab_widget.addTab(self.options_analyzer_tab, "Options Analyzer")

        # Connect the universal control panel's data_fetched signal to the tabs
        self.universal_controls.data_fetched.connect(self.market_odds_tab.update_data_from_universal)
        self.universal_controls.data_fetched.connect(self.candlestick_tab.update_from_market_odds)
        self.universal_controls.data_fetched.connect(self.options_analyzer_tab.on_data_fetched_universal)

        # Keep the original connections for backward compatibility
        self.market_odds_tab.data_fetched.connect(self.candlestick_tab.update_from_market_odds)

        # Data tab: for displaying OHLC data in a table
        self.data_tab = DataTab(self.tab_widget, market_odds_tab=self.market_odds_tab)
        self.tab_widget.addTab(self.data_tab, "Data")

        # Connect the Data tab to both data_fetched signals
        self.market_odds_tab.data_fetched.connect(self.data_tab.refresh_data)
        self.universal_controls.data_fetched.connect(self.data_tab.refresh_data)

        # Volatility Statistics tab (combines Volatility Graph, Density Graph, and FWL Odds)
        self.Volatility_Statistics_tab = VolatilityStatisticsTab(self.tab_widget, data_tab=self.data_tab)
        self.tab_widget.addTab(self.Volatility_Statistics_tab, "Volatility Statistics")

        # Connect Volatility Statistics tab to universal controls (explicit connection for consistency)
        self.universal_controls.data_fetched.connect(self.Volatility_Statistics_tab.on_data_fetched_universal)

        # Market XRAY tab for options data collection
        self.market_xray_tab = MarketXRAYTab(self.tab_widget)
        self.tab_widget.addTab(self.market_xray_tab, "Market XRAY")

        # Connect Market XRAY tab to universal controls
        self.universal_controls.data_fetched.connect(self.market_xray_tab.on_data_fetched_universal)

        # TradingView Scraper tab for TradingView data fetching
        self.tradingview_scraper_tab = TradingViewScraperTabWidget(self.tab_widget)
        self.tab_widget.addTab(self.tradingview_scraper_tab, "TradingView Scraper")

        # Connect TradingView Scraper tab to universal controls (optional for future integration)
        self.universal_controls.data_fetched.connect(self.tradingview_scraper_tab.on_data_fetched_universal)

        # Data Warehouse tab for displaying system data folder contents
        self.data_warehouse_tab = DataWarehouseTab(self.tab_widget)
        self.tab_widget.addTab(self.data_warehouse_tab, "Data Warehouse")

        # Connect Data Warehouse tab to universal controls (optional for future integration)
        self.universal_controls.data_fetched.connect(self.data_warehouse_tab.on_data_fetched_universal)

        # Seasonality tab for seasonality analysis
        self.seasonality_tab = SeasonalityTab(self.tab_widget)
        self.tab_widget.addTab(self.seasonality_tab, "Seasonality")

        # Connect Seasonality tab to universal controls (optional for future integration)
        self.universal_controls.data_fetched.connect(self.seasonality_tab.on_data_fetched_universal)

        # Game Theory tab for game theory analysis and simulations
        self.game_theory_tab = GameTheoryTab(self.tab_widget)
        self.tab_widget.addTab(self.game_theory_tab, "Game Theory")

        # Connect Game Theory tab to universal controls (optional for future integration)
        self.universal_controls.data_fetched.connect(self.game_theory_tab.on_data_fetched_universal)



        # Schwab API Login functionality is now integrated into Market Odds tab
        # No separate tab needed since login button is available in Data Connections

        # Keep references to individual graph tabs for backward compatibility
        self.volatility_graph_tab = self.Volatility_Statistics_tab.volatility_graph_tab
        self.density_graph_tab = self.Volatility_Statistics_tab.density_graph_tab
        self.fwl_odds_tab = self.Volatility_Statistics_tab.fwl_odds_tab

        # Adaptive Learning and Online Learning tabs have been removed

        # Set default startup size to 800x600 after all widgets are created
        self.resize(800, 600)

    def init_dialog_manager(self):
        """Initialize the dialog manager with settings from parameter registry."""
        dialog_manager = DialogManager.get_instance()

        # Configure dialog suppression based on parameter registry settings
        if default_registry.get_parameter('suppress_dialogs'):
            dialog_manager.set_suppress_dialogs(default_registry.get_value('suppress_dialogs'))

        if default_registry.get_parameter('suppress_info_dialogs'):
            dialog_manager.set_suppress_info_dialogs(default_registry.get_value('suppress_info_dialogs'))

        if default_registry.get_parameter('suppress_warning_dialogs'):
            dialog_manager.set_suppress_warning_dialogs(default_registry.get_value('suppress_warning_dialogs'))

        if default_registry.get_parameter('suppress_error_dialogs'):
            dialog_manager.set_suppress_error_dialogs(default_registry.get_value('suppress_error_dialogs'))

        # Enable logging of suppressed messages
        dialog_manager.set_log_suppressed_messages(True)

    def on_schwab_login_success(self):
        """Handle successful Schwab API login"""
        logger.info("Schwab API login successful")

        # Update universal controls to show Schwab is available
        if hasattr(self.universal_controls, 'update_data_source_status'):
            self.universal_controls.update_data_source_status()

    def on_schwab_login_failed(self, error_message: str):
        """Handle failed Schwab API login"""
        logger.warning(f"Schwab API login failed: {error_message}")

        # Show error message to user using dialog manager
        from dialog_manager import warning
        warning(
            self,
            "Schwab API Login Failed",
            f"Failed to connect to Schwab API:\n\n{error_message}\n\nYou can still use Yahoo Finance data."
        )

    def apply_theme_to_components(self, theme_colors):
        """Apply the theme colors to all components in the application.

        Args:
            theme_colors: Dictionary of theme colors
        """
        # Apply theme to main window
        self.setStyleSheet(f"background-color: {theme_colors['background']}; color: {theme_colors['text']};")

        # Tab widget styling is now handled by the global theme stylesheet
        # No need for separate tab widget styling here

        # Apply theme to Market Odds tab
        if hasattr(self.market_odds_tab, 'chart_colors'):
            self.market_odds_tab.chart_colors.update({
                'background': theme_colors['background'],
                'text': theme_colors['text'],
                'grid': theme_colors['grid'],
                'bullish': theme_colors['bullish'],
                'bearish': theme_colors['bearish'],
                'vector': theme_colors['vector'],
                'axis': theme_colors['axis'],
                'line': theme_colors['line'],
                'primary_accent': theme_colors['primary_accent']
            })

            # Force redraw if possible
            if hasattr(self.market_odds_tab, 'plot_data') and callable(self.market_odds_tab.plot_data):
                try:
                    self.market_odds_tab.plot_data()
                except:
                    pass

        # Apply theme to Candlestick Chart tab
        if hasattr(self.candlestick_tab, 'chart_colors'):
            self.candlestick_tab.chart_colors.update({
                'background': theme_colors['background'],
                'text': theme_colors['text'],
                'grid': theme_colors['grid'],
                'bullish': theme_colors['bullish'],
                'bearish': theme_colors['bearish'],
                'axis': theme_colors['axis'],
                'atr_1d': theme_colors['atr_1d']
            })

            # Force redraw if possible
            if hasattr(self.candlestick_tab, 'plot_data') and callable(self.candlestick_tab.plot_data):
                try:
                    self.candlestick_tab.plot_data()
                except:
                    pass

        # Apply theme to Volatility Statistics tab
        if hasattr(self.Volatility_Statistics_tab, 'setStyleSheet'):
            self.Volatility_Statistics_tab.setStyleSheet(f"background-color: {theme_colors['background']};")

            # Update control panel styling if possible
            if hasattr(self.Volatility_Statistics_tab, 'init_ui') and callable(self.Volatility_Statistics_tab.init_ui):
                try:
                    # Re-initialize UI with updated theme colors
                    self.Volatility_Statistics_tab.init_ui()
                except:
                    pass

        # Apply theme to Volatility Graph tab
        if hasattr(self.volatility_graph_tab, 'chart_colors'):
            self.volatility_graph_tab.chart_colors.update({
                'background': theme_colors['background'],
                'text': theme_colors['text'],
                'bullish': theme_colors['bullish'],
                'bearish': theme_colors['bearish'],
                'line': theme_colors['line'],
                'current_price': theme_colors['highlight']
            })

        # Apply theme to Density Graph tab
        if hasattr(self.density_graph_tab, 'chart_colors'):
            self.density_graph_tab.chart_colors.update({
                'background': theme_colors['background'],
                'text': theme_colors['text'],
                'grid': theme_colors['grid'],
                'bullish': theme_colors['bullish'],
                'bearish': theme_colors['bearish'],
                'neutral': theme_colors['neutral'],
                'highlight': theme_colors['highlight']
            })

        # Apply theme to Data tab
        if hasattr(self.data_tab, 'setStyleSheet'):
            self.data_tab.setStyleSheet(f"background-color: {theme_colors['background']};")

        # Apply theme to Market XRAY tab
        if hasattr(self, 'market_xray_tab') and hasattr(self.market_xray_tab, 'apply_theme'):
            try:
                self.market_xray_tab.apply_theme()
            except Exception as e:
                logger.debug(f"Could not apply theme to Market XRAY tab: {str(e)}")

        # Apply theme to Data Warehouse tab
        if hasattr(self, 'data_warehouse_tab') and hasattr(self.data_warehouse_tab, 'chart_colors'):
            self.data_warehouse_tab.chart_colors.update({
                'background': theme_colors['background'],
                'text': theme_colors['text'],
                'grid': theme_colors['grid'],
                'bullish': theme_colors['bullish'],
                'bearish': theme_colors['bearish'],
                'axis': theme_colors['axis']
            })

            # Update chart widget background if it exists
            if hasattr(self.data_warehouse_tab, 'chart_widget'):
                self.data_warehouse_tab.chart_widget.setBackground(theme_colors['background'])



        # Apply theme to all tabs and components
        # The theme module is already imported in the main function
        # Styling is now handled by the global theme stylesheet
        # No need for hardcoded styles here

        # Option Greeks tab has been removed - no zones to plot
        pass

    def plot_data(self):
        self.plot_widget.clear()

        if self.data is None or len(self.data) == 0:
            return

        # Create timestamp array
        timestamps = np.arange(len(self.data))

        # Create candlestick data
        candlestick_data = []
        for idx, (timestamp, row) in enumerate(self.data.iterrows()):
            candlestick_data.append((
                idx,  # x-coordinate
                row['Open'],
                row['High'],
                row['Low'],
                row['Close']
            ))

        # Create and add candlestick item
        self.candlestick_item = CandlestickItem(
            candlestick_data,
            bullish_color=self.chart_colors['bullish'],
            bearish_color=self.chart_colors['bearish'],
            chart_colors=self.chart_colors,
            show_signals=True,
            show_pullbacks=True,
            show_reversals=True
        )
        self.plot_widget.addItem(self.candlestick_item)

        # Add ATR bands
        self.plot_atr_bands()

        # Add peak/trough lines from market odds tab
        self.plot_peak_trough_lines()

        # Add Volume Profile if enabled
        if self.show_vp.isChecked():
            # Calculate lookback range for volume profile
            if self.display_options['vp_use_visible_range']:
                lookback = len(self.data)
            else:
                lookback = min(self.display_options['vp_lookback_depth'], len(self.data))

            # Use recent data based on lookback
            display_data = self.data.iloc[-lookback:]

            # Create volume profile data with proper x-coordinates
            volume_profile_data = []
            data_end_idx = len(self.data) - 1  # Index of last data point

            for i, (timestamp, row) in enumerate(display_data.iterrows()):
                # Map to the correct x-coordinate on the chart
                chart_idx = data_end_idx - lookback + i + 1

                volume_profile_data.append((
                    chart_idx,  # Correct x-coordinate
                    row['Open'],
                    row['High'],
                    row['Low'],
                    row['Close'],
                    row['Volume'] if 'Volume' in row else 1.0  # Default volume if not available
                ))

            # Calculate price range for volume profile
            highest_price = display_data['High'].max()
            lowest_price = display_data['Low'].min()

            # Add padding to the price range to ensure all prices are covered
            price_padding = (highest_price - lowest_price) * 0.05
            price_range = (highest_price + price_padding, lowest_price - price_padding)

            # Parse color options
            vp_bar_color = self.parse_color(self.display_options.get('vp_bar_color', 'gray'))
            vp_poc_color = self.parse_color(self.display_options.get('vp_poc_color', 'red'))
            vp_va_color = self.parse_color(self.display_options.get('vp_va_color', 'blue'))

            # Create volume profile item with optimized settings
            self.volume_profile_item = VolumeProfileItem(
                volume_profile_data,
                price_range,
                num_bars=self.display_options['vp_num_bars'],
                bar_thickness=self.display_options['vp_bar_thickness'],
                bar_length_mult=self.display_options['vp_bar_length_mult'],
                right_offset=self.display_options['vp_right_offset'],
                volume_type=self.display_options['vp_volume_type'],
                bar_color=vp_bar_color,
                display_poc=self.display_options['vp_display_poc'],
                poc_line_color=vp_poc_color,
                poc_line_thickness=self.display_options['vp_poc_line_thickness'],
                display_va=self.display_options['vp_display_va'],
                va_percent=self.display_options['vp_va_percent'],
                va_bar_color=vp_va_color,
                display_va_lines=self.display_options['vp_display_va_lines'],
                va_lines_thickness=self.display_options['vp_va_lines_thickness'],
                right_aligned=self.display_options['vp_right_aligned']
            )
            self.plot_widget.addItem(self.volume_profile_item)

        # Add crosshair
        self.plot_widget.addItem(self.vLine, ignoreBounds=True)
        self.plot_widget.addItem(self.hLine, ignoreBounds=True)

        # Set axis range
        self.plot_widget.setXRange(0, len(self.data))
        price_range = self.data['High'].max() - self.data['Low'].min()
        price_padding = price_range * 0.1
        self.plot_widget.setYRange(
            self.data['Low'].min() - price_padding,
            self.data['High'].max() + price_padding
        )

        # Add signals legend if available
        if hasattr(self, 'create_signals_legend'):
            self.create_signals_legend()

    def parse_color(self, color_str):
        """Parse color from CSS-style color to tuple for PyQtGraph"""
        if color_str.startswith('#'):
            # Convert hex to RGB
            color = QtGui.QColor(color_str)
            return (color.red(), color.green(), color.blue(), 180)  # Add alpha
        elif color_str in ['red', 'green', 'blue', 'yellow', 'gray', 'white', 'black']:
            # Handle named colors
            color = QtGui.QColor(color_str)
            return (color.red(), color.green(), color.blue(), 180)  # Add alpha
        else:
            # Default to gray if can't parse
            return (100, 100, 100, 180)

    def mouseMoved(self, evt):
        pos = evt[0]
        if self.plot_widget.sceneBoundingRect().contains(pos):
            mousePoint = self.plot_widget.plotItem.vb.mapSceneToView(pos)

            # Update drawing if in progress
            if self.drawing_tools.drawing:
                self.drawing_tools.update_drawing(mousePoint)

            # Update crosshair
            self.vLine.setPos(mousePoint.x())
            self.hLine.setPos(mousePoint.y())

            # Update price info using round() for consistent candlestick selection
            index = round(mousePoint.x())
            if self.data is not None and 0 <= index < len(self.data):
                time = self.data.index[index]
                price = self.data.iloc[index]['Close']
                volume_text = ""
                if 'Volume' in self.data.columns:
                    volume = self.data.iloc[index]['Volume']
                    volume_text = f" | V: {volume:.2f}"

                self.time_price_label.setText(
                    f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')} | "
                    f"Price: {price:.2f} | "
                    f"O: {self.data.iloc[index]['Open']:.2f} | "
                    f"H: {self.data.iloc[index]['High']:.2f} | "
                    f"L: {self.data.iloc[index]['Low']:.2f} | "
                    f"C: {self.data.iloc[index]['Close']:.2f}{volume_text}"
                )

                # Create or update overlay text for crosshairs
                if not hasattr(self, 'crosshair_overlay'):
                    # Create a semi-transparent overlay for the crosshair text
                    self.crosshair_overlay = pg.TextItem(
                        html=f'<div style="background-color: rgba(0,0,0,0.7); padding: 5px; border-radius: 5px; color: white;">' +
                             f'<span style="font-weight: bold;">X:</span> {time.strftime("%Y-%m-%d %H:%M:%S")}<br>' +
                             f'<span style="font-weight: bold;">Y:</span> {price:.2f}' +
                             f'</div>',
                        anchor=(0, 0)
                    )
                    self.plot_widget.addItem(self.crosshair_overlay)

                # Position the overlay near the crosshair intersection
                x_pos = mousePoint.x()
                y_pos = mousePoint.y()

                # Update the overlay text
                self.crosshair_overlay.setHtml(
                    f'<div style="background-color: rgba(0,0,0,0.7); padding: 5px; border-radius: 5px; color: white;">' +
                    f'<span style="font-weight: bold;">X:</span> {time.strftime("%Y-%m-%d %H:%M:%S")}<br>' +
                    f'<span style="font-weight: bold;">Y:</span> {price:.2f}' +
                    f'</div>'
                )

                # Position the overlay to avoid going off-screen
                view_range = self.plot_widget.viewRange()
                x_min, x_max = view_range[0]
                y_min, y_max = view_range[1]

                # Adjust position based on mouse location
                if x_pos > (x_min + (x_max - x_min) * 0.7):
                    # If mouse is on the right side, place overlay to the left
                    anchor_x = 1.0
                else:
                    # If mouse is on the left side, place overlay to the right
                    anchor_x = 0.0

                if y_pos > (y_min + (y_max - y_min) * 0.7):
                    # If mouse is on the bottom, place overlay above
                    anchor_y = 1.0
                else:
                    # If mouse is on the top, place overlay below
                    anchor_y = 0.0

                self.crosshair_overlay.setAnchor((anchor_x, anchor_y))
                self.crosshair_overlay.setPos(x_pos, y_pos)
            else:
                # Hide overlay text if out of range
                if hasattr(self, 'crosshair_overlay'):
                    self.crosshair_overlay.setHtml("")
        else:
            # Hide overlay text if mouse is outside the plot
            if hasattr(self, 'crosshair_overlay'):
                self.crosshair_overlay.setHtml("")

    def get_market_odds_tab(self):
        """Get the market odds tab from the parent window"""
        # Find the parent MainWindow
        parent = self.parent()
        while parent and not hasattr(parent, 'market_odds_tab'):
            parent = parent.parent()

        # Return the market odds tab if found
        if parent and hasattr(parent, 'market_odds_tab'):
            return parent.market_odds_tab
        return None



    def update_from_market_odds(self, symbol, timeframe, days_to_load):
        """Update the chart with data from the market odds tab"""
        if not symbol:
            return

        # Update the UI to reflect the market odds tab settings
        self.symbol_input.setText(symbol)
        self.timeframe_combo.setCurrentText(timeframe)
        self.days_spin.setValue(days_to_load)

        # Get the market odds tab to access its data
        market_odds_tab = self.get_market_odds_tab()
        if market_odds_tab and hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None:
            # Use the data from the market odds tab
            self.data = market_odds_tab.data.copy()
            self.symbol = symbol
            self.timeframe = timeframe
            self.days_to_load = days_to_load

            # Plot the data
            self.plot_data()

            # Update the status
            print(f"Candlestick chart updated with data from Market Odds tab: {symbol} {timeframe} {days_to_load}d")

    def fetch_data(self):
        # Get symbol from input field
        symbol = self.symbol_input.text().strip().upper()
        if not symbol:
            from dialog_manager import warning
            warning(self, "Error", "Please enter a symbol")
            return

        try:
            QtWidgets.QApplication.setOverrideCursor(QtCore.Qt.CursorShape.WaitCursor)

            # Use data dispatcher instead of direct yfinance call
            from data_dispatcher import DataDispatcher, DataFetchThread

            days = self.days_spin.value()
            interval = self.timeframe_combo.currentText()

            # Create a data fetch thread to get Yahoo Finance data
            fetch_thread = DataFetchThread(symbol, interval, days, 'yfinance')
            self.data = fetch_thread._fetch_yfinance_data()

            if self.data.empty:
                from dialog_manager import warning
                warning(self, "Error", "No data returned for symbol")
                QtWidgets.QApplication.restoreOverrideCursor()
                return

            if self.data.index.tz is not None:
                self.data.index = self.data.index.tz_localize(None)

            self.plot_data()
            self.update_timer.start()

            QtWidgets.QApplication.restoreOverrideCursor()

        except Exception as e:
            from dialog_manager import critical
            critical(self, "Error", f"Error fetching data: {str(e)}")
            QtWidgets.QApplication.restoreOverrideCursor()

    def update_data(self):
        if not self.symbol_input.text().strip():
            return

        try:
            symbol = self.symbol_input.text().strip().upper()

            # Use data dispatcher instead of direct yfinance call
            from data_dispatcher import DataFetchThread

            interval = self.timeframe_combo.currentText()

            # Create a data fetch thread to get Yahoo Finance data for updates
            fetch_thread = DataFetchThread(symbol, interval, 1, 'yfinance')  # 1 day for updates
            new_data = fetch_thread._fetch_yfinance_data()

            if new_data.empty:
                return

            if new_data.index.tz is not None:
                new_data.index = new_data.index.tz_localize(None)

            if len(new_data) > 0 and (self.data is None or new_data.index[-1] > self.data.index[-1]):
                if self.data is not None:
                    combined = pd.concat([self.data, new_data])
                    self.data = combined[~combined.index.duplicated(keep='last')]
                else:
                    self.data = new_data

                self.plot_data()

        except Exception as e:
            print(f"Error updating data: {str(e)}")
            self.update_timer.stop()

    # Add new methods for settings management

    def show_settings_dialog(self):
        """Show the settings dialog and initialize it with current values"""
        self.settings_dialog = SettingsDialog(self)

        # Set default values since we're using DummyClassifier
        if hasattr(self, 'vector_length'):
            self.settings_dialog.vector_length_spin.setValue(self.vector_length)

        # Use default values for ML settings since DummyClassifier doesn't have these attributes
        self.settings_dialog.lookback_spin.setValue(5)
        self.settings_dialog.lookahead_spin.setValue(10)
        self.settings_dialog.reversal_threshold_spin.setValue(0.5)
        self.settings_dialog.enable_classifier.setChecked(False)  # Disabled since using dummy
        self.settings_dialog.confidence_threshold_spin.setValue(0.65)

        # Set default cache settings
        self.settings_dialog.adaptive_cache_check.setChecked(True)
        self.settings_dialog.cache_size_spin.setValue(1000)
        self.settings_dialog.min_cache_size_spin.setValue(100)
        self.settings_dialog.max_cache_size_spin.setValue(10000)

        # Set default model type
        self.settings_dialog.model_type_combo.setCurrentText("auto")

        # Set default calibration settings
        self.settings_dialog.enable_calibration.setChecked(False)

        self.settings_dialog.show()

    def open_settings_dialog(self):
        """Open the settings dialog"""
        if self.settings_dialog.exec() == QtWidgets.QDialog.DialogCode.Accepted:
            # Update chart colors
            new_colors = self.settings_dialog.get_chart_colors()
            self.chart_colors = new_colors

            # Update display options
            new_options = self.settings_dialog.get_display_options()
            self.display_options = new_options

            # Update drawing settings
            new_drawing_settings = self.settings_dialog.get_drawing_settings()
            self.drawing_settings = new_drawing_settings

            # Skip cache settings update since we're using DummyClassifier
            # Cache settings are not applicable to the dummy classifier

            # Skip adaptive learning settings since we're using DummyClassifier
            # Adaptive learning is not applicable to the dummy classifier

            # Set default color in the color combo
            default_color = self.drawing_settings['default_color']
            index = self.color_combo.findText(default_color)
            if index >= 0:
                self.color_combo.setCurrentIndex(index)

            # Apply changes to chart
            self.apply_settings()

            # Save settings
            self.save_chart_settings()

    def apply_w1_daily_settings(self):
        """Apply W1/Daily preset settings

        This function applies the W1/Daily preset settings extracted from extract.py.
        It sets up the chart to display a simple line of closing prices with a timeframe of 1d.
        """
        # Clear any cached data first
        if hasattr(self, '_vector_cache'):
            self._vector_cache.clear()
        if hasattr(self, '_plot_data_cache'):
            self._plot_data_cache.clear()

        # Set timeframe to 1d
        self.timeframe_combo.setCurrentText("1d")

        # Set data length to 200
        if hasattr(self, 'dtl_spin'):
            self.dtl_spin.setValue(200)

        # Set vector length to 1 (simple line of closing prices)
        if hasattr(self, 'settings_dialog') and hasattr(self.settings_dialog, 'vector_length_spin'):
            self.settings_dialog.vector_length_spin.setValue(1)

        # Set vector color to purple for W1/Daily preset
        if hasattr(self, 'chart_colors'):
            self.chart_colors['vector'] = '#800080'  # Purple color for W1/Daily vector

        # Disable ML features since W1/Daily uses a simple line of closing prices
        if hasattr(self, 'settings_dialog'):
            if hasattr(self.settings_dialog, 'enable_classifier'):
                self.settings_dialog.enable_classifier.setChecked(False)

            # Signal generation settings
            if hasattr(self.settings_dialog, 'lookback_spin'):
                self.settings_dialog.lookback_spin.setValue(5)
            if hasattr(self.settings_dialog, 'lookahead_spin'):
                self.settings_dialog.lookahead_spin.setValue(10)
            if hasattr(self.settings_dialog, 'reversal_threshold_spin'):
                self.settings_dialog.reversal_threshold_spin.setValue(0.50)
            if hasattr(self.settings_dialog, 'confidence_threshold_spin'):
                self.settings_dialog.confidence_threshold_spin.setValue(0.65)
            if hasattr(self.settings_dialog, 'confirmation_period_spin'):
                self.settings_dialog.confirmation_period_spin.setValue(1)  # Set to 1 for immediate signal generation
            if hasattr(self.settings_dialog, 'min_crossing_magnitude_spin'):
                self.settings_dialog.min_crossing_magnitude_spin.setValue(0.01)  # Set to small value for sensitivity
            if hasattr(self.settings_dialog, 'enable_signal_filter'):
                self.settings_dialog.enable_signal_filter.setChecked(True)
            if hasattr(self.settings_dialog, 'enable_pivot_constraint'):
                self.settings_dialog.enable_pivot_constraint.setChecked(True)

        # Visual settings
        if hasattr(self, 'show_vector'):
            self.show_vector.setChecked(True)
        if hasattr(self, 'show_peak_trough_rays'):
            self.show_peak_trough_rays.setChecked(False)  # Disable peak/trough rays for W1/Daily

        # Apply settings
        if hasattr(self, 'vector_length_changed'):
            self.vector_length_changed()
        if hasattr(self, 'update_classifier_settings'):
            self.update_classifier_settings()
        if hasattr(self, 'update_signal_filter_settings'):
            self.update_signal_filter_settings()
        if hasattr(self, 'data') and self.data is not None and hasattr(self, 'plot_data_until'):
            self.plot_data_until(self.current_idx)
        elif hasattr(self, 'data') and self.data is not None and hasattr(self, 'plot_data'):
            self.plot_data()

    def apply_settings(self):
        """Apply current settings to the chart"""
        # Update plot widget
        self.plot_widget.setBackground(self.chart_colors['background'])

        # Update grid visibility based on settings
        if self.display_options['show_grid']:
            self.plot_widget.showGrid(x=True, y=True, alpha=self.display_options['grid_opacity'])
        else:
            self.plot_widget.showGrid(x=False, y=False)

        # Update axes style
        self.plot_widget.getAxis('left').setTextPen(self.chart_colors['text'])
        self.plot_widget.getAxis('bottom').setTextPen(self.chart_colors['text'])
        self.plot_widget.getAxis('left').setPen(self.chart_colors['axis'])
        self.plot_widget.getAxis('bottom').setPen(self.chart_colors['axis'])

        # Update labels
        self.plot_widget.setLabel('left', f'<div style="color: {self.chart_colors["text"]};">Price</div>')
        self.plot_widget.setLabel('bottom', f'<div style="color: {self.chart_colors["text"]};">Time</div>')

        # Update candlestick colors if item exists
        if self.candlestick_item is not None:
            self.candlestick_item.updateColors(
                self.chart_colors['bullish'],
                self.chart_colors['bearish']
            )

        # Update ATR checkbox state
        self.show_atr.setChecked(self.display_options['show_atr'])

        # Update Volume Profile checkbox state
        if hasattr(self, 'show_vp'):
            self.show_vp.setChecked(self.display_options['show_volume_profile'])

        # Update timer interval
        self.update_timer.setInterval(self.display_options['update_interval'])

        # Replot data if available
        if self.data is not None:
            self.plot_data()

    def save_chart_settings(self):
        """Save settings to QSettings"""
        settings = QtCore.QSettings('CandlestickChartApp', 'ChartAppearance')

        # Save colors
        for key, value in self.chart_colors.items():
            settings.setValue(f'chart_color_{key}', value)

        # Save display options
        for key, value in self.display_options.items():
            settings.setValue(f'display_option_{key}', value)

        settings.sync()

    def load_chart_settings(self):
        """Load settings from QSettings"""
        settings = QtCore.QSettings('CandlestickChartApp', 'ChartAppearance')

        # Load colors
        for key in self.chart_colors.keys():
            setting_key = f'chart_color_{key}'
            if settings.contains(setting_key):
                self.chart_colors[key] = settings.value(setting_key)

        # Load display options
        for key in self.display_options.keys():
            setting_key = f'display_option_{key}'
            if settings.contains(setting_key):
                # Handle different types of values
                value = settings.value(setting_key)

                # Convert string booleans to actual booleans
                if isinstance(value, str):
                    if value.lower() == 'true':
                        value = True
                    elif value.lower() == 'false':
                        value = False
                    elif value.replace('.', '', 1).isdigit():
                        # Convert string numbers to float or int
                        value = float(value)
                        if value.is_integer():
                            value = int(value)

                self.display_options[key] = value

    # Add drawing tool methods
    def set_drawing_tool(self, tool_type):
        """Set the current drawing tool"""
        # Uncheck all buttons
        for btn in [self.line_btn, self.rect_btn, self.hline_btn, self.vline_btn, self.text_btn]:
            btn.setChecked(False)

        # If selecting the same tool, deactivate it
        if self.current_drawing_tool == tool_type:
            self.current_drawing_tool = None
            return

        # Set the current tool and check its button
        self.current_drawing_tool = tool_type

        if tool_type == 'line':
            self.line_btn.setChecked(True)
        elif tool_type == 'rectangle':
            self.rect_btn.setChecked(True)
        elif tool_type == 'horizontal':
            self.hline_btn.setChecked(True)
        elif tool_type == 'vertical':
            self.vline_btn.setChecked(True)
        elif tool_type == 'text':
            self.text_btn.setChecked(True)

    def clear_all_drawings(self):
        """Clear all drawings from the chart"""
        self.drawing_tools.clear_all()

    def mouse_clicked(self, event):
        """Handle mouse clicks for drawing"""
        if event.button() == QtCore.Qt.MouseButton.LeftButton and self.current_drawing_tool:
            pos = self.plot_widget.plotItem.vb.mapSceneToView(event.scenePos())

            # If drawing is in progress, finish it
            if self.drawing_tools.drawing:
                self.drawing_tools.finish_drawing(pos)

                # If the tool is 'text', prompt for text
                if self.current_drawing_tool == 'text':
                    text, ok = QtWidgets.QInputDialog.getText(
                        self, "Add Text", "Enter text:"
                    )
                    if ok and text:
                        # Create text item with custom size
                        text_item = pg.TextItem(text=text, color=self.drawing_tools.colors[self.color_combo.currentText()],
                                               anchor=(0.5, 0.5))
                        font = QtGui.QFont()
                        font.setPointSize(self.drawing_settings['text_size'])
                        text_item.setFont(font)
                        text_item.setPos(pos.x(), pos.y())
                        self.plot_widget.addItem(text_item)
                        self.drawing_tools.drawing_items.append(text_item)
                return

            # Get current color from combo or default setting
            color_name = self.color_combo.currentText()
            color = self.drawing_tools.colors[color_name]

            # Start drawing with selected tool
            if self.current_drawing_tool == 'text':
                text, ok = QtWidgets.QInputDialog.getText(
                    self, "Add Text", "Enter text:"
                )
                if ok and text:
                    # Create text item with custom size
                    text_item = pg.TextItem(text=text, color=color, anchor=(0.5, 0.5))
                    font = QtGui.QFont()
                    font.setPointSize(self.drawing_settings['text_size'])
                    text_item.setFont(font)
                    text_item.setPos(pos.x(), pos.y())
                    self.plot_widget.addItem(text_item)
                    self.drawing_tools.drawing_items.append(text_item)
                return

            self.drawing_tools.start_drawing(pos, self.current_drawing_tool)

            # Set color and width for the current item
            if self.drawing_tools.current_item:
                if isinstance(self.drawing_tools.current_item, pg.PlotDataItem):
                    self.drawing_tools.current_item.setPen(
                        pg.mkPen(color, width=self.drawing_settings['line_width'])
                    )
                elif isinstance(self.drawing_tools.current_item, pg.InfiniteLine):
                    self.drawing_tools.current_item.setPen(
                        pg.mkPen(color, width=self.drawing_settings['line_width'])
                    )
                else:
                    self.drawing_tools.change_item_color(
                        self.drawing_tools.current_item,
                        color
                    )

        elif event.button() == QtCore.Qt.MouseButton.RightButton:
            # Handle right click
            pos = self.plot_widget.plotItem.vb.mapSceneToView(event.scenePos())

            # Check if clicked on a drawing item
            if self.drawing_tools.select_item(pos):
                self.show_drawing_context_menu(event.screenPos())

    def mouseMoved(self, evt):
        pos = evt[0]
        if self.plot_widget.sceneBoundingRect().contains(pos):
            mousePoint = self.plot_widget.plotItem.vb.mapSceneToView(pos)

            # Update drawing if in progress
            if self.drawing_tools.drawing:
                self.drawing_tools.update_drawing(mousePoint)

            # Update crosshair
            self.vLine.setPos(mousePoint.x())
            self.hLine.setPos(mousePoint.y())

            # Update price info with OHLC values using round() for consistency
            index = round(mousePoint.x())
            if self.data is not None and 0 <= index < len(self.data):
                time = self.data.index[index]
                open_price = self.data.iloc[index]['Open']
                high_price = self.data.iloc[index]['High']
                low_price = self.data.iloc[index]['Low']
                close_price = self.data.iloc[index]['Close']
                self.time_price_label.setText(
                    f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')} | "
                    f"Open: {open_price:.2f} | "
                    f"High: {high_price:.2f} | "
                    f"Low: {low_price:.2f} | "
                    f"Close: {close_price:.2f}"
                )

                # Check if mouse is near a signal marker
                if self.candlestick_item is not None:
                    # Check if we're hovering over a signal using the same index for consistency
                    if self.data is not None and 0 <= index < len(self.data) and index in self.candlestick_item.signals:
                        signal_type = self.candlestick_item.signals[index]
                        signal_time = self.data.index[index].strftime('%Y-%m-%d %H:%M:%S')
                        signal_price = self.data.iloc[index]['Close']

                        # Determine color based on signal type
                        if 'BullishPullback' in signal_type:
                            color = self.chart_colors.get('bullish_pullback', '#00BCD4')
                            description = "Bullish Pullback Signal"
                        elif 'BearishPullback' in signal_type:
                            color = self.chart_colors.get('bearish_pullback', '#FF5722')
                            description = "Bearish Pullback Signal"
                        elif 'BullishReversal' in signal_type:
                            color = self.chart_colors.get('bullish_reversal', '#4CAF50')
                            description = "Bullish Reversal Signal"
                        elif 'BearishReversal' in signal_type:
                            color = self.chart_colors.get('bearish_reversal', '#F44336')
                            description = "Bearish Reversal Signal"
                        elif 'Pullback' in signal_type:  # Legacy type
                            color = self.chart_colors.get('pullback', '#00BCD4')
                            description = "Pullback Signal"
                        elif 'Reversal' in signal_type:  # Legacy type
                            color = self.chart_colors.get('reversal', '#F44336')
                            description = "Reversal Signal"
                        else:
                            color = '#FFFFFF'
                            description = "Signal"

                        # Set signal info with HTML styling for color
                        self.signal_info_label.setText(
                            f"<span style='color:{color};'>● {description}</span> | "
                            f"Time: {signal_time} | "
                            f"Price: {signal_price:.2f}"
                        )
                    else:
                        # Clear signal info if not hovering over a signal
                        self.signal_info_label.setText("")

    def show_drawing_context_menu(self, position):
        """Show context menu for a selected drawing item"""
        menu = QtWidgets.QMenu()

        color_menu = menu.addMenu("Change Color")
        for color_name, color_value in self.drawing_tools.colors.items():
            action = color_menu.addAction(color_name)
            action.triggered.connect(
                lambda checked, color=color_value: self.drawing_tools.change_item_color(
                    self.drawing_tools.selected_item, color
                )
            )

        add_label_action = menu.addAction("Add/Edit Label")
        add_label_action.triggered.connect(self.add_label_to_selected_item)

        delete_action = menu.addAction("Delete")
        delete_action.triggered.connect(
            lambda: self.drawing_tools.delete_item(self.drawing_tools.selected_item)
        )

        menu.exec(QtCore.QPoint(int(position.x()), int(position.y())))

    def add_label_to_selected_item(self):
        """Add or edit label for selected drawing item"""
        if self.drawing_tools.selected_item:
            text, ok = QtWidgets.QInputDialog.getText(
                self, "Add Label", "Enter label text:"
            )
            if ok and text:
                self.drawing_tools.add_label_to_item(self.drawing_tools.selected_item, text)

# Function to ensure cursor is restored
def restore_cursor():
    """Ensure the application cursor is restored to default"""
    try:
        # Restore any application-level cursor overrides
        # This is a safety measure to ensure cursor is visible
        QtWidgets.QApplication.restoreOverrideCursor()
    except Exception:
        pass

def main():

    def message_handler(mode, context, message):

        pass

    # Install the custom message handler
    QtCore.qInstallMessageHandler(message_handler)

    # Suppress warnings from all modules
    import warnings
    warnings.filterwarnings("ignore")

    # Redirect stdout and stderr to null device - COMMENTED OUT FOR DEBUGGING
    import sys, os
    # sys.stdout = open(os.devnull, 'w')
    # sys.stderr = open(os.devnull, 'w')

    app = QtWidgets.QApplication(sys.argv)
    app.setStyle("Fusion")

    # Enable high DPI scaling
    if hasattr(QtCore.Qt, 'AA_EnableHighDpiScaling'):
        QtWidgets.QApplication.setAttribute(QtCore.Qt.AA_EnableHighDpiScaling, True)
    if hasattr(QtCore.Qt, 'AA_UseHighDpiPixmaps'):
        QtWidgets.QApplication.setAttribute(QtCore.Qt.AA_UseHighDpiPixmaps, True)

    # Import and apply Dark Grey theme
    import theme
    app.setStyleSheet(theme.get_global_stylesheet(theme.DEFAULT))

    # Create global theme colors variable for access throughout the application
    global THEME_COLORS
    THEME_COLORS = theme.DEFAULT

    # Ensure cursor is restored before creating the main window
    restore_cursor()

    # Create and show the main window
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()