"""
Game Theory Tab for DataDriven Application

This module provides a tab for game theory analysis with simulation capabilities.
Includes input controls for winrate, risk-to-reward ratio, steps, and simulations.
"""

import pandas as pd
import numpy as np
from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
import logging
from datetime import datetime
import random

# Configure logging
logger = logging.getLogger(__name__)

# Theme colors for consistency with the application
THEME_COLORS = {
    'background': '#1E1E1E',               # Dark background
    'control_panel': '#2A2A2A',           # Control panel background
    'text': '#E0E0E0',                    # Light gray text
    'grid': '#404040',                    # Grid lines
    'highlight': '#FFC107',               # Amber highlight
    'button_bg': '#3A3A3A',              # Button background
    'button_hover': '#4A4A4A',           # Button hover
    'button_pressed': '#2A2A2A',         # Button pressed
    'button_shadow': '0 4px 6px rgba(0, 122, 204, 0.3)',  # Button shadow
    'bullish': '#4CAF50',                # Material Design Green
    'bearish': '#F44336',                # Material Design Red
    'borders': '#404040',                # Border color
}


class GameTheoryTab(QtWidgets.QWidget):
    """
    Tab for game theory analysis and simulations.
    Provides controls for winrate, risk-to-reward, steps, and simulations.
    """
    
    def __init__(self, parent=None):
        """
        Initialize the game theory tab.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.parent = parent
        
        # Initialize chart colors
        self.chart_colors = {
            'background': THEME_COLORS['background'],
            'text': THEME_COLORS['text'],
            'grid': THEME_COLORS['grid'],
            'bullish': THEME_COLORS['bullish'],
            'bearish': THEME_COLORS['bearish'],
            'axis': THEME_COLORS['text']
        }
        
        # Initialize UI
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create input controls section at the top
        self.create_input_controls(main_layout)
        
        # Create chart area below the controls
        self.create_chart_area(main_layout)
        
    def create_input_controls(self, parent_layout):
        """Create the input controls section at the top of the tab."""
        # Controls container
        controls_widget = QtWidgets.QWidget()
        controls_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS['control_panel']};
                border-bottom: 1px solid {THEME_COLORS['borders']};
            }}
        """)
        controls_widget.setFixedHeight(60)
        
        # Controls layout
        controls_layout = QtWidgets.QHBoxLayout(controls_widget)
        controls_layout.setContentsMargins(20, 10, 20, 10)
        controls_layout.setSpacing(30)
        
        # Winrate % input
        winrate_layout = QtWidgets.QHBoxLayout()
        winrate_label = QtWidgets.QLabel("Winrate %:")
        winrate_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 12px; font-weight: bold;")
        self.winrate_spinbox = QtWidgets.QDoubleSpinBox()
        self.winrate_spinbox.setRange(0.0, 100.0)
        self.winrate_spinbox.setValue(50.0)
        self.winrate_spinbox.setDecimals(1)
        self.winrate_spinbox.setSuffix("%")
        self.winrate_spinbox.setStyleSheet(self.get_spinbox_style())
        self.winrate_spinbox.setToolTip("Percentage of trades that are profitable (0-100%)")
        winrate_layout.addWidget(winrate_label)
        winrate_layout.addWidget(self.winrate_spinbox)
        
        # Risk-to-Reward input
        rr_layout = QtWidgets.QHBoxLayout()
        rr_label = QtWidgets.QLabel("Risk-to-Reward:")
        rr_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 12px; font-weight: bold;")
        self.rr_spinbox = QtWidgets.QDoubleSpinBox()
        self.rr_spinbox.setRange(0.1, 10.0)
        self.rr_spinbox.setValue(2.0)
        self.rr_spinbox.setDecimals(1)
        self.rr_spinbox.setStyleSheet(self.get_spinbox_style())
        self.rr_spinbox.setToolTip("Ratio of profit to loss per trade (e.g., 2.0 = win $2 for every $1 risked)")
        rr_layout.addWidget(rr_label)
        rr_layout.addWidget(self.rr_spinbox)
        
        # Steps input
        steps_layout = QtWidgets.QHBoxLayout()
        steps_label = QtWidgets.QLabel("Steps:")
        steps_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 12px; font-weight: bold;")
        self.steps_spinbox = QtWidgets.QSpinBox()
        self.steps_spinbox.setRange(10, 10000)
        self.steps_spinbox.setValue(100)
        self.steps_spinbox.setStyleSheet(self.get_spinbox_style())
        self.steps_spinbox.setToolTip("Number of trades to simulate in each run")
        steps_layout.addWidget(steps_label)
        steps_layout.addWidget(self.steps_spinbox)
        
        # Simulations input
        simulations_layout = QtWidgets.QHBoxLayout()
        simulations_label = QtWidgets.QLabel("Simulations:")
        simulations_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-size: 12px; font-weight: bold;")
        self.simulations_spinbox = QtWidgets.QSpinBox()
        self.simulations_spinbox.setRange(1, 1000)
        self.simulations_spinbox.setValue(100)
        self.simulations_spinbox.setStyleSheet(self.get_spinbox_style())
        self.simulations_spinbox.setToolTip("Number of independent simulation runs to perform")
        simulations_layout.addWidget(simulations_label)
        simulations_layout.addWidget(self.simulations_spinbox)
        
        # Run simulation button
        self.run_button = QtWidgets.QPushButton("Run Simulation")
        self.run_button.setStyleSheet(self.get_button_style())
        self.run_button.setToolTip("Execute Monte Carlo simulation with current parameters")
        self.run_button.clicked.connect(self.run_simulation)
        
        # Add all controls to the layout
        controls_layout.addLayout(winrate_layout)
        controls_layout.addLayout(rr_layout)
        controls_layout.addLayout(steps_layout)
        controls_layout.addLayout(simulations_layout)
        controls_layout.addWidget(self.run_button)
        controls_layout.addStretch()  # Push everything to the left
        
        parent_layout.addWidget(controls_widget)
        
    def create_chart_area(self, parent_layout):
        """Create the chart area below the controls."""
        # Create horizontal layout for chart and statistics
        chart_layout = QtWidgets.QHBoxLayout()

        # Chart widget (takes most of the space)
        self.chart_widget = pg.PlotWidget()
        self.chart_widget.setBackground(THEME_COLORS['background'])
        self.chart_widget.setLabel('left', 'Portfolio Value ($)', color=THEME_COLORS['text'])
        self.chart_widget.setLabel('bottom', 'Steps', color=THEME_COLORS['text'])
        self.chart_widget.setTitle('Game Theory Simulation Results', color=THEME_COLORS['text'])

        # Style the chart
        self.chart_widget.getAxis('left').setPen(pg.mkPen(color=THEME_COLORS['text']))
        self.chart_widget.getAxis('bottom').setPen(pg.mkPen(color=THEME_COLORS['text']))
        self.chart_widget.getAxis('left').setTextPen(pg.mkPen(color=THEME_COLORS['text']))
        self.chart_widget.getAxis('bottom').setTextPen(pg.mkPen(color=THEME_COLORS['text']))

        # Enable grid
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)

        # Statistics panel (right side)
        self.create_statistics_panel()

        # Add to layout with proportions
        chart_layout.addWidget(self.chart_widget, 3)  # Chart takes 3/4 of space
        chart_layout.addWidget(self.stats_widget, 1)   # Stats takes 1/4 of space

        # Create container widget for the chart layout
        chart_container = QtWidgets.QWidget()
        chart_container.setLayout(chart_layout)

        parent_layout.addWidget(chart_container)

    def create_statistics_panel(self):
        """Create the statistics panel on the right side."""
        self.stats_widget = QtWidgets.QWidget()
        self.stats_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS['control_panel']};
                border-left: 1px solid {THEME_COLORS['borders']};
            }}
        """)
        self.stats_widget.setFixedWidth(250)

        stats_layout = QtWidgets.QVBoxLayout(self.stats_widget)
        stats_layout.setContentsMargins(15, 15, 15, 15)
        stats_layout.setSpacing(10)

        # Title
        title_label = QtWidgets.QLabel("Simulation Statistics")
        title_label.setStyleSheet(f"""
            color: {THEME_COLORS['text']};
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        stats_layout.addWidget(title_label)

        # Statistics labels
        self.stats_labels = {}
        stat_names = [
            "Expected Value:",
            "Profitable Sims:",
            "Average Final:",
            "Best Case:",
            "Worst Case:",
            "Sharpe Ratio:",
            "Max Drawdown:",
            "Win Streak:",
            "Loss Streak:"
        ]

        for stat_name in stat_names:
            label = QtWidgets.QLabel(f"{stat_name} --")
            label.setStyleSheet(f"""
                color: {THEME_COLORS['text']};
                font-size: 11px;
                margin: 2px 0px;
            """)
            self.stats_labels[stat_name] = label
            stats_layout.addWidget(label)

        # Add stretch to push everything to the top
        stats_layout.addStretch()

    def get_spinbox_style(self):
        """Get the stylesheet for spinboxes."""
        return f"""
            QSpinBox, QDoubleSpinBox {{
                background-color: {THEME_COLORS['button_bg']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 12px;
                min-width: 80px;
            }}
            QSpinBox:hover, QDoubleSpinBox:hover {{
                background-color: {THEME_COLORS['button_hover']};
                border-color: {THEME_COLORS['highlight']};
            }}
            QSpinBox:focus, QDoubleSpinBox:focus {{
                border-color: {THEME_COLORS['highlight']};
                outline: none;
            }}
        """
        
    def get_button_style(self):
        """Get the stylesheet for buttons."""
        return f"""
            QPushButton {{
                background-color: {THEME_COLORS['button_bg']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS['button_hover']};
                border-color: {THEME_COLORS['highlight']};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS['button_pressed']};
            }}
        """
        
    def run_simulation(self):
        """Run the game theory simulation with current parameters."""
        # Get parameters from UI
        winrate = self.winrate_spinbox.value() / 100.0  # Convert percentage to decimal
        risk_reward = self.rr_spinbox.value()
        steps = self.steps_spinbox.value()
        num_simulations = self.simulations_spinbox.value()

        logger.info(f"Running simulation: winrate={winrate:.1%}, RR={risk_reward}, steps={steps}, sims={num_simulations}")

        # Clear previous results
        self.chart_widget.clear()

        # Run simulations
        simulation_results = []
        final_values = []

        for sim in range(num_simulations):
            portfolio_values = self.run_single_simulation(winrate, risk_reward, steps)
            simulation_results.append(portfolio_values)
            final_values.append(portfolio_values[-1])

            # Plot individual simulations with different transparency based on final outcome
            x_data = list(range(len(portfolio_values)))
            final_value = portfolio_values[-1]

            # Color based on performance
            if final_value > 100:  # Profitable
                color = pg.mkColor(THEME_COLORS['bullish'])
                color.setAlpha(30)
            elif final_value > 50:  # Moderate loss
                color = pg.mkColor(255, 255, 0)  # Yellow
                color.setAlpha(30)
            else:  # Heavy loss
                color = pg.mkColor(THEME_COLORS['bearish'])
                color.setAlpha(30)

            self.chart_widget.plot(x_data, portfolio_values, pen=pg.mkPen(color=color, width=1))

        # Calculate and plot statistics
        if simulation_results:
            # Calculate percentiles and average
            max_length = max(len(sim) for sim in simulation_results)
            padded_results = []

            for sim in simulation_results:
                # Pad shorter simulations with their final value
                if len(sim) < max_length:
                    padded_sim = sim + [sim[-1]] * (max_length - len(sim))
                else:
                    padded_sim = sim
                padded_results.append(padded_sim)

            padded_results = np.array(padded_results)

            # Calculate statistics
            avg_portfolio = np.mean(padded_results, axis=0)
            percentile_75 = np.percentile(padded_results, 75, axis=0)
            percentile_25 = np.percentile(padded_results, 25, axis=0)

            x_data = list(range(len(avg_portfolio)))

            # Plot percentile bands
            fill_brush = pg.mkBrush(color=pg.mkColor(THEME_COLORS['highlight']))
            fill_brush.setAlpha(50)
            self.chart_widget.plot(x_data, percentile_75, pen=pg.mkPen(color=THEME_COLORS['highlight'], width=1, style=QtCore.Qt.PenStyle.DashLine))
            self.chart_widget.plot(x_data, percentile_25, pen=pg.mkPen(color=THEME_COLORS['highlight'], width=1, style=QtCore.Qt.PenStyle.DashLine))

            # Fill between percentiles
            fill_item = pg.FillBetweenItem(
                pg.PlotCurveItem(x_data, percentile_75),
                pg.PlotCurveItem(x_data, percentile_25),
                brush=fill_brush
            )
            self.chart_widget.addItem(fill_item)

            # Plot average line
            self.chart_widget.plot(x_data, avg_portfolio, pen=pg.mkPen(color=THEME_COLORS['highlight'], width=3), name='Average')

            # Add horizontal line at starting value
            self.chart_widget.addLine(y=100, pen=pg.mkPen(color=THEME_COLORS['text'], width=1, style=QtCore.Qt.PenStyle.DotLine))

            # Update chart title with statistics
            profitable_sims = sum(1 for val in final_values if val > 100)
            profit_rate = profitable_sims / len(final_values) * 100
            avg_final = np.mean(final_values)

            title = f'Game Theory Simulation - {profit_rate:.1f}% Profitable | Avg Final: ${avg_final:.2f}'
            self.chart_widget.setTitle(title, color=THEME_COLORS['text'])

            # Update statistics panel
            self.update_statistics(simulation_results, final_values, winrate, risk_reward)

        # Add legend
        self.chart_widget.addLegend()
        
    def run_single_simulation(self, winrate, risk_reward, steps):
        """Run a single simulation and return portfolio values over time."""
        portfolio_value = 100.0  # Start with $100
        portfolio_values = [portfolio_value]

        # Risk management: risk a percentage of current portfolio
        risk_per_trade = 0.02  # Risk 2% of portfolio per trade

        for step in range(steps):
            # Calculate position size based on current portfolio value
            risk_amount = portfolio_value * risk_per_trade

            # Skip trade if portfolio is too small
            if portfolio_value < 1.0:
                portfolio_values.append(portfolio_value)
                continue

            # Determine if this trade wins or loses
            if random.random() < winrate:
                # Win: gain risk_reward times the risk amount
                gain = risk_amount * risk_reward
                portfolio_value += gain
            else:
                # Loss: lose the risk amount
                portfolio_value -= risk_amount

            # Ensure portfolio doesn't go negative
            portfolio_value = max(0, portfolio_value)
            portfolio_values.append(portfolio_value)

            # Stop if portfolio is wiped out
            if portfolio_value <= 0:
                # Fill remaining steps with zeros
                portfolio_values.extend([0] * (steps - step))
                break

        return portfolio_values

    def update_statistics(self, simulation_results, final_values, winrate, risk_reward):
        """Update the statistics panel with simulation results."""
        if not simulation_results or not final_values:
            return

        # Calculate statistics
        profitable_sims = sum(1 for val in final_values if val > 100)
        profit_rate = profitable_sims / len(final_values) * 100
        avg_final = np.mean(final_values)
        best_case = max(final_values)
        worst_case = min(final_values)

        # Expected value calculation
        expected_value = (winrate * risk_reward) - ((1 - winrate) * 1)

        # Calculate Sharpe ratio (simplified)
        returns = [(val - 100) / 100 for val in final_values]
        avg_return = np.mean(returns)
        std_return = np.std(returns)
        sharpe_ratio = avg_return / std_return if std_return > 0 else 0

        # Calculate max drawdown
        max_drawdown = 0
        for sim in simulation_results:
            peak = sim[0]
            for value in sim:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak if peak > 0 else 0
                max_drawdown = max(max_drawdown, drawdown)

        # Calculate win/loss streaks (simplified)
        win_streak = 0
        loss_streak = 0
        current_win_streak = 0
        current_loss_streak = 0

        for val in final_values:
            if val > 100:
                current_win_streak += 1
                current_loss_streak = 0
                win_streak = max(win_streak, current_win_streak)
            else:
                current_loss_streak += 1
                current_win_streak = 0
                loss_streak = max(loss_streak, current_loss_streak)

        # Update labels
        self.stats_labels["Expected Value:"].setText(f"Expected Value: {expected_value:.3f}")
        self.stats_labels["Profitable Sims:"].setText(f"Profitable Sims: {profit_rate:.1f}%")
        self.stats_labels["Average Final:"].setText(f"Average Final: ${avg_final:.2f}")
        self.stats_labels["Best Case:"].setText(f"Best Case: ${best_case:.2f}")
        self.stats_labels["Worst Case:"].setText(f"Worst Case: ${worst_case:.2f}")
        self.stats_labels["Sharpe Ratio:"].setText(f"Sharpe Ratio: {sharpe_ratio:.3f}")
        self.stats_labels["Max Drawdown:"].setText(f"Max Drawdown: {max_drawdown:.1%}")
        self.stats_labels["Win Streak:"].setText(f"Win Streak: {win_streak}")
        self.stats_labels["Loss Streak:"].setText(f"Loss Streak: {loss_streak}")

    def on_data_fetched_universal(self, symbol, data):
        """
        Handle data fetched from universal controls.
        
        Args:
            symbol: The symbol that was fetched
            data: The fetched data
        """
        # This tab doesn't need to respond to universal control data changes
        # but we provide this method for consistency with other tabs
        pass
