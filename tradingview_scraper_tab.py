"""
TradingView Scraper Tab for Main Application

This module provides a tab widget that integrates the TradingView data scraper
into the main application's tab system. It uses ONLY TradingView data sources
and NO Yahoo Finance.

*** USES ZERO YAHOO FINANCE - ONLY TRADINGVIEW SOURCES ***

Features:
- TradingView WebSocket API integration
- TVDataFeed library support (optional)
- PyQtGraph charts with black background
- Interactive crosshair with OHLC data display
- Data export functionality
- Futures contract support with month codes
"""

from PyQt6.QtWidgets import QWidget
from TVscraper import TradingViewScraperTab


class TradingViewScraperTabWidget(QWidget):
    """
    TradingView scraper tab widget for integration into the main application.
    
    This is a wrapper around the TradingViewScraperTab to ensure proper
    integration with the main application's tab system.
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Create the main scraper tab widget
        self.scraper_tab = TradingViewScraperTab(self)
        
        # Set up layout to contain the scraper tab
        from PyQt6.QtWidgets import QVBoxLayout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)  # Remove margins for seamless integration
        layout.addWidget(self.scraper_tab)
    
    def on_data_fetched_universal(self, data):
        """
        Handle data fetched from universal controls (if needed for integration).
        
        This method can be connected to the main application's data_fetched signal
        if integration with other tabs is desired in the future.
        """
        # For now, the TradingView scraper operates independently
        # Future integration could update the scraper's symbol input based on
        # universal controls if desired
        pass


# For backward compatibility and direct import
TradingViewTab = TradingViewScraperTabWidget
